@import "@styles/variables";
@import "@styles/mixins";

.extension-description {
  padding: 57px 0 0;
  position: relative;

  &__tabs {
    &-menu-bar {
      display: flex;
      align-items: center;

      @media (max-width: ( $md + 40)) {
        align-items: flex-end;
        flex-direction: column-reverse;
      }

      h2 {
        flex-grow: 1;
        margin: 0;
        letter-spacing: -0.16px;
        line-height: 32px;
        font-size: 32px;
        font-style: normal;
        font-weight: 800;
        font-family: $bricolage-font-family; font-optical-sizing: none;

        @include rtl-styles {
          font-family: $arabic-font-family;
        }

        @media (max-width: ( $md + 40)) {
          width: 100%;
          text-align: left;
          margin: 22px 0;
        }
      }
    }
  }
}

.tab-panel {
  padding: 32px 0 112px;

  @media (max-width: ( $md + 40)) {
    padding: 10px 0 22px;
  }
}
