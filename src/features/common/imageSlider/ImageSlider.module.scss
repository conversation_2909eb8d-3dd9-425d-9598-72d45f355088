.slider {
  width: 384px;
  text-align: center;
  position: relative;
}

.progress-container {
  width: 90%;
  height: 6px;
  // background-color: #ddd;
  border-radius: 3px;
  overflow: hidden;
  // margin-bottom: 15px;
  display: flex;
  gap: 2%;
  position: absolute;
  z-index: 1000;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
}

.bar-segment {
  height: 4px;
  background-color: #ccc;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
  flex: 1;
}

.bar-segment:last-child {
  margin-right: 0;
}

.bar-segment.active {
  background-color: #eee;
}

@keyframes scaleProgress {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

.progress-bar,
.progress-bar-arabic {
  position: absolute;
  top: 0;
  height: 100%;
  background-color: black;
  width: 100%; // Full width; animation controls visual size
  transform: scaleX(0);
  animation: scaleProgress 3s linear forwards;

  &.paused {
    animation-play-state: paused !important;
  }
}
.progress-bar {
  transform-origin: left;
}

.progress-bar-arabic {
  transform-origin: right;
}

.main-image {
  width: 100%;
  object-fit: fill;
  border-radius: 24px;
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.5s ease-in-out;
  opacity: 0;
  background-color: transparent; // crucial to avoid white bg!

}

.main-image.fade-out {
  opacity: 0;
}


.image-container {
  position: relative;
  width: 100%;
  height: 450px;
  cursor: pointer;
  // background-color: #000; // or match your design to avoid flashing


  img{
    height: 450px;
  }
}



.fade-in {
  opacity: 1;
  z-index: 2;
}

.fade-out {
  opacity: 0;
  z-index: 1;
}

.hidden {
  opacity: 0;
    pointer-events: none;
    z-index: 0;
}
