@import "/src/styles/variables";
@import "/src/styles/mixins";
.marketing-banners{
    display: flex;
    justify-content: center;
    gap: 64px;
    height: 450px;
    margin-top: 64px;
    margin-bottom: 64px;
}

.single-banner{
    width: 832px;
    height: 450px;
    border-radius: 24px 0px 24px 24px;
    position: relative;
   
    img{
        width: 832px;
        height: 450px;
        object-fit: cover;
    }

    &__button{
        cursor: pointer;
        position: absolute;
        bottom: 34px;
        left: 48px;
        display: flex;
        width: 302px;
        height: 50px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;
        border-radius: 12px;
        border: 2px solid var(--Black-Black---Primary, #0E0F0C);
        background: var(--Black-Black---Primary, #0E0F0C);

        @include rtl-styles{
            left: unset;
            right: 48px;
        }

        img{
            width: 24px;
            height: 24px;
            flex-shrink: 0;
            @include rtl-styles{
                transform: rotateY(180deg);
            }
        }

        span{
            color: var(--White-White---Primary, #FFF);
            text-align: right;
            /* Other/Text - Button */
            font-family: "Mona Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: var(--Font-Line-height-lg, 24px); /* 150% */
            letter-spacing: -0.16px;

        }
    }

}